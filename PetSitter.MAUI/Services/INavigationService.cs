namespace PetSitter.MAUI.Services
{
    public interface INavigationService
    {
        Task NavigateToAsync(string route, IDictionary<string, object>? parameters = null);
        Task NavigateBackAsync();
        Task PopToRootAsync();
        Task ShowModalAsync(string route, IDictionary<string, object>? parameters = null);
        Task CloseModalAsync();
        Task DisplayAlertAsync(string title, string message, string cancel = "OK");
        Task<bool> DisplayConfirmAsync(string title, string message, string accept = "Yes", string cancel = "No");
        Task<string> DisplayActionSheetAsync(string title, string cancel, string? destruction, params string[] buttons);
    }
}
