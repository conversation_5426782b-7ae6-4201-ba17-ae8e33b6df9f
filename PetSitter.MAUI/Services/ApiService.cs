using PetSitter.MAUI.Models;
using System.Text;
using System.Text.Json;

namespace PetSitter.MAUI.Services
{
    public class ApiService : IApiService
    {
        private readonly HttpClient _httpClient;
        private readonly IAuthService _authService;
        private readonly string _baseUrl;

        public ApiService(HttpClient httpClient, IAuthService authService)
        {
            _httpClient = httpClient;
            _authService = authService;
            _baseUrl = DeviceInfo.Platform == DevicePlatform.Android 
                ? "http://********:5000" // Android emulator localhost
                : "http://localhost:5000"; // iOS simulator and other platforms
        }

        public async Task<ApiResponse<T>> GetAsync<T>(string endpoint)
        {
            try
            {
                await SetAuthHeaderAsync();
                var response = await _httpClient.GetAsync($"{_baseUrl}{endpoint}");
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                return new ApiResponse<T> { Success = false, Error = ex.Message };
            }
        }

        public async Task<ApiResponse<T>> PostAsync<T>(string endpoint, object data)
        {
            try
            {
                await SetAuthHeaderAsync();
                var json = JsonSerializer.Serialize(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await _httpClient.PostAsync($"{_baseUrl}{endpoint}", content);
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                return new ApiResponse<T> { Success = false, Error = ex.Message };
            }
        }

        public async Task<ApiResponse<T>> PutAsync<T>(string endpoint, object data)
        {
            try
            {
                await SetAuthHeaderAsync();
                var json = JsonSerializer.Serialize(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await _httpClient.PutAsync($"{_baseUrl}{endpoint}", content);
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                return new ApiResponse<T> { Success = false, Error = ex.Message };
            }
        }

        public async Task<ApiResponse<T>> DeleteAsync<T>(string endpoint)
        {
            try
            {
                await SetAuthHeaderAsync();
                var response = await _httpClient.DeleteAsync($"{_baseUrl}{endpoint}");
                return await ProcessResponseAsync<T>(response);
            }
            catch (Exception ex)
            {
                return new ApiResponse<T> { Success = false, Error = ex.Message };
            }
        }

        public async Task<ApiResponse<List<PetCareRequest>>> GetPetCareRequestsAsync()
        {
            return await GetAsync<List<PetCareRequest>>("/api/petcarerequests");
        }

        public async Task<ApiResponse<PetCareRequest>> CreatePetCareRequestAsync(PetCareRequest request)
        {
            return await PostAsync<PetCareRequest>("/api/petcarerequests", request);
        }

        public async Task<ApiResponse<PetCareRequest>> UpdatePetCareRequestAsync(PetCareRequest request)
        {
            return await PutAsync<PetCareRequest>($"/api/petcarerequests/{request.Id}", request);
        }

        public async Task<ApiResponse<bool>> DeletePetCareRequestAsync(int requestId)
        {
            return await DeleteAsync<bool>($"/api/petcarerequests/{requestId}");
        }

        public async Task<ApiResponse<List<Booking>>> GetBookingsAsync()
        {
            return await GetAsync<List<Booking>>("/api/bookings");
        }

        public async Task<ApiResponse<Booking>> CreateBookingAsync(Booking booking)
        {
            return await PostAsync<Booking>("/api/bookings", booking);
        }

        public async Task<ApiResponse<Booking>> UpdateBookingAsync(Booking booking)
        {
            return await PutAsync<Booking>($"/api/bookings/{booking.Id}", booking);
        }

        public async Task<ApiResponse<bool>> CancelBookingAsync(int bookingId)
        {
            return await DeleteAsync<bool>($"/api/bookings/{bookingId}");
        }

        public async Task<ApiResponse<UserProfile>> GetUserProfileAsync(string userId)
        {
            return await GetAsync<UserProfile>($"/api/users/{userId}");
        }

        public async Task<ApiResponse<UserProfile>> UpdateUserProfileAsync(UserProfile profile)
        {
            return await PutAsync<UserProfile>($"/api/users/{profile.Id}", profile);
        }

        private async Task SetAuthHeaderAsync()
        {
            var token = await _authService.GetTokenAsync();
            if (!string.IsNullOrEmpty(token))
            {
                _httpClient.DefaultRequestHeaders.Authorization = 
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            }
        }

        private async Task<ApiResponse<T>> ProcessResponseAsync<T>(HttpResponseMessage response)
        {
            var content = await response.Content.ReadAsStringAsync();
            
            if (response.IsSuccessStatusCode)
            {
                try
                {
                    var data = JsonSerializer.Deserialize<T>(content, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });
                    
                    return new ApiResponse<T> 
                    { 
                        Success = true, 
                        Data = data 
                    };
                }
                catch (JsonException ex)
                {
                    return new ApiResponse<T> 
                    { 
                        Success = false, 
                        Error = $"Failed to parse response: {ex.Message}" 
                    };
                }
            }
            
            return new ApiResponse<T> 
            { 
                Success = false, 
                Error = $"API call failed: {response.StatusCode} - {content}" 
            };
        }
    }
}
