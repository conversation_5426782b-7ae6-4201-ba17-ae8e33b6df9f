using PetSitter.MAUI.Models;

namespace PetSitter.MAUI.Services
{
    public interface IApiService
    {
        Task<ApiResponse<T>> GetAsync<T>(string endpoint);
        Task<ApiResponse<T>> PostAsync<T>(string endpoint, object data);
        Task<ApiResponse<T>> PutAsync<T>(string endpoint, object data);
        Task<ApiResponse<T>> DeleteAsync<T>(string endpoint);
        
        // Pet Care Requests
        Task<ApiResponse<List<PetCareRequest>>> GetPetCareRequestsAsync();
        Task<ApiResponse<PetCareRequest>> CreatePetCareRequestAsync(PetCareRequest request);
        Task<ApiResponse<PetCareRequest>> UpdatePetCareRequestAsync(PetCareRequest request);
        Task<ApiResponse<bool>> DeletePetCareRequestAsync(int requestId);
        
        // Bookings
        Task<ApiResponse<List<Booking>>> GetBookingsAsync();
        Task<ApiResponse<Booking>> CreateBookingAsync(Booking booking);
        Task<ApiResponse<Booking>> UpdateBookingAsync(Booking booking);
        Task<ApiResponse<bool>> CancelBookingAsync(int bookingId);
        
        // User Profile
        Task<ApiResponse<UserProfile>> GetUserProfileAsync(string userId);
        Task<ApiResponse<UserProfile>> UpdateUserProfileAsync(UserProfile profile);
    }
}
