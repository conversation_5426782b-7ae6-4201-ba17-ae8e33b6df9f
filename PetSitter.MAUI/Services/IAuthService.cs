using PetSitter.MAUI.Models;

namespace PetSitter.MAUI.Services
{
    public interface IAuthService
    {
        Task<AuthResponse> LoginAsync(LoginRequest request);
        Task<AuthResponse> RegisterAsync(AuthRequest request);
        Task<bool> LogoutAsync();
        Task<bool> IsAuthenticatedAsync();
        Task<UserProfile?> GetCurrentUserAsync();
        Task<string?> GetTokenAsync();
        Task SaveTokenAsync(string token);
        Task ClearTokenAsync();
        event EventHandler<bool> AuthenticationStateChanged;
    }
}
