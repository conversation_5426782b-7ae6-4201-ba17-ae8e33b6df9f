using PetSitter.MAUI.Models;
using System.Text;
using System.Text.Json;

namespace PetSitter.MAUI.Services
{
    public class AuthService : IAuthService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl;
        private UserProfile? _currentUser;

        public event EventHandler<bool>? AuthenticationStateChanged;

        public AuthService(HttpClient httpClient)
        {
            _httpClient = httpClient;
            _baseUrl = DeviceInfo.Platform == DevicePlatform.Android 
                ? "http://********:5000" // Android emulator localhost
                : "http://localhost:5000"; // iOS simulator and other platforms
        }

        public async Task<AuthResponse> LoginAsync(LoginRequest request)
        {
            try
            {
                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/login", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var loginResponse = JsonSerializer.Deserialize<AuthResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    if (loginResponse?.Success == true && !string.IsNullOrEmpty(loginResponse.UserId))
                    {
                        // Create a simple token (in production, use proper JWT)
                        var token = $"{loginResponse.UserId}:{loginResponse.Role}";
                        await SaveTokenAsync(token);
                        
                        // Load user profile
                        await LoadCurrentUserAsync(loginResponse.UserId);
                        
                        AuthenticationStateChanged?.Invoke(this, true);
                        return loginResponse;
                    }
                }

                return new AuthResponse 
                { 
                    Success = false, 
                    Error = "Invalid credentials" 
                };
            }
            catch (Exception ex)
            {
                return new AuthResponse 
                { 
                    Success = false, 
                    Error = $"Login failed: {ex.Message}" 
                };
            }
        }

        public async Task<AuthResponse> RegisterAsync(AuthRequest request)
        {
            try
            {
                var json = JsonSerializer.Serialize(request);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                
                var response = await _httpClient.PostAsync($"{_baseUrl}/api/auth/register", content);
                var responseContent = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    var registerResponse = JsonSerializer.Deserialize<AuthResponse>(responseContent, new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true
                    });

                    return registerResponse ?? new AuthResponse { Success = false, Error = "Registration failed" };
                }

                return new AuthResponse 
                { 
                    Success = false, 
                    Error = "Registration failed" 
                };
            }
            catch (Exception ex)
            {
                return new AuthResponse 
                { 
                    Success = false, 
                    Error = $"Registration failed: {ex.Message}" 
                };
            }
        }

        public async Task<bool> LogoutAsync()
        {
            try
            {
                await ClearTokenAsync();
                _currentUser = null;
                AuthenticationStateChanged?.Invoke(this, false);
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> IsAuthenticatedAsync()
        {
            var token = await GetTokenAsync();
            return !string.IsNullOrEmpty(token);
        }

        public async Task<UserProfile?> GetCurrentUserAsync()
        {
            if (_currentUser != null)
                return _currentUser;

            var token = await GetTokenAsync();
            if (string.IsNullOrEmpty(token))
                return null;

            var parts = token.Split(':');
            if (parts.Length >= 2)
            {
                await LoadCurrentUserAsync(parts[0]);
            }

            return _currentUser;
        }

        public async Task<string?> GetTokenAsync()
        {
            return await SecureStorage.GetAsync("auth_token");
        }

        public async Task SaveTokenAsync(string token)
        {
            await SecureStorage.SetAsync("auth_token", token);
        }

        public async Task ClearTokenAsync()
        {
            SecureStorage.Remove("auth_token");
            await Task.CompletedTask;
        }

        private async Task LoadCurrentUserAsync(string userId)
        {
            try
            {
                // In a real app, you would call an API to get user details
                // For now, we'll create a basic user profile from stored data
                var token = await GetTokenAsync();
                if (!string.IsNullOrEmpty(token))
                {
                    var parts = token.Split(':');
                    if (parts.Length >= 2)
                    {
                        _currentUser = new UserProfile
                        {
                            Id = parts[0],
                            Role = parts[1],
                            // Other properties would be loaded from API
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                // Log error
                System.Diagnostics.Debug.WriteLine($"Failed to load user: {ex.Message}");
            }
        }
    }
}
