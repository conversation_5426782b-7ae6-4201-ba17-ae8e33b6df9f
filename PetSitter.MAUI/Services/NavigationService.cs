namespace PetSitter.MAUI.Services
{
    public class NavigationService : INavigationService
    {
        public async Task NavigateToAsync(string route, IDictionary<string, object>? parameters = null)
        {
            try
            {
                if (parameters != null && parameters.Any())
                {
                    await Shell.Current.GoToAsync(route, parameters);
                }
                else
                {
                    await Shell.Current.GoToAsync(route);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Navigation error: {ex.Message}");
                throw;
            }
        }

        public async Task NavigateBackAsync()
        {
            try
            {
                await Shell.Current.GoToAsync("..");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Navigation back error: {ex.Message}");
                throw;
            }
        }

        public async Task PopToRootAsync()
        {
            try
            {
                await Shell.Current.GoToAsync("//");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Pop to root error: {ex.Message}");
                throw;
            }
        }

        public async Task ShowModalAsync(string route, IDictionary<string, object>? parameters = null)
        {
            try
            {
                var modalRoute = $"//{route}";
                if (parameters != null && parameters.Any())
                {
                    await Shell.Current.GoToAsync(modalRoute, parameters);
                }
                else
                {
                    await Shell.Current.GoToAsync(modalRoute);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Show modal error: {ex.Message}");
                throw;
            }
        }

        public async Task CloseModalAsync()
        {
            try
            {
                await Shell.Current.GoToAsync("..");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Close modal error: {ex.Message}");
                throw;
            }
        }

        public async Task DisplayAlertAsync(string title, string message, string cancel = "OK")
        {
            try
            {
                if (Application.Current?.MainPage != null)
                {
                    await Application.Current.MainPage.DisplayAlert(title, message, cancel);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Display alert error: {ex.Message}");
            }
        }

        public async Task<bool> DisplayConfirmAsync(string title, string message, string accept = "Yes", string cancel = "No")
        {
            try
            {
                if (Application.Current?.MainPage != null)
                {
                    return await Application.Current.MainPage.DisplayAlert(title, message, accept, cancel);
                }
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Display confirm error: {ex.Message}");
                return false;
            }
        }

        public async Task<string> DisplayActionSheetAsync(string title, string cancel, string? destruction, params string[] buttons)
        {
            try
            {
                if (Application.Current?.MainPage != null)
                {
                    return await Application.Current.MainPage.DisplayActionSheet(title, cancel, destruction, buttons);
                }
                return cancel;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Display action sheet error: {ex.Message}");
                return cancel;
            }
        }
    }
}
