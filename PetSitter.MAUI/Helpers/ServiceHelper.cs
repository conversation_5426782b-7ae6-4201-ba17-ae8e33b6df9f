using PetSitter.MAUI.Services;
using PetSitter.MAUI.ViewModels;

namespace PetSitter.MAUI.Helpers
{
    public static class ServiceHelper
    {
        public static IServiceCollection RegisterServices(this IServiceCollection services)
        {
            // Register HttpClient
            services.AddHttpClient();

            // Register Services
            services.AddSingleton<INavigationService, NavigationService>();
            services.AddSingleton<IAuthService, AuthService>();
            services.AddSingleton<IApiService, ApiService>();

            return services;
        }

        public static IServiceCollection RegisterViewModels(this IServiceCollection services)
        {
            // Register ViewModels
            services.AddTransient<LoginViewModel>();
            services.AddTransient<RegisterViewModel>();
            services.AddTransient<MainViewModel>();
            services.AddTransient<ProfileViewModel>();
            services.AddTransient<PetCareRequestsViewModel>();
            services.AddTransient<BookingsViewModel>();
            services.AddTransient<ChatViewModel>();

            return services;
        }

        public static IServiceCollection RegisterViews(this IServiceCollection services)
        {
            // Register Views - will be added when we create them
            // services.AddTransient<LoginPage>();
            // services.AddTransient<RegisterPage>();
            // etc.

            return services;
        }

        public static T GetService<T>() where T : class
        {
            return Application.Current?.Handler?.MauiContext?.Services.GetService<T>()
                ?? throw new InvalidOperationException($"Service of type {typeof(T).Name} not found.");
        }

        public static object GetService(Type serviceType)
        {
            return Application.Current?.Handler?.MauiContext?.Services.GetService(serviceType)
                ?? throw new InvalidOperationException($"Service of type {serviceType.Name} not found.");
        }
    }
}
