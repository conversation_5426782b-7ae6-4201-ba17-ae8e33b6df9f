﻿<?xml version="1.0" encoding="utf-8"?>
<manifest package="com.companyname.PetSitter.MAUI" version="1.0.0" api-version="7" xmlns="http://tizen.org/ns/packages">
  <profile name="common" />
  <ui-application appid="com.companyname.PetSitter.MAUI" exec="PetSitter.MAUI.dll" multiple="false" nodisplay="false" taskmanage="true" type="dotnet" launch_mode="single">
    <label>PetSitter.MAUI</label>
    <icon>appicon.xhigh.png</icon>
    <metadata key="http://tizen.org/metadata/prefer_dotnet_aot" value="true" />
  </ui-application>
  <shortcut-list />
  <privileges>
    <privilege>http://tizen.org/privilege/internet</privilege>
  </privileges> 
  <dependencies />
  <provides-appdefined-privileges />
</manifest>