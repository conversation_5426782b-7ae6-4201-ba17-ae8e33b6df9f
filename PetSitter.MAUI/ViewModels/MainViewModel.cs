using PetSitter.MAUI.Services;

namespace PetSitter.MAUI.ViewModels
{
    public partial class MainViewModel : BaseViewModel
    {
        private readonly IAuthService _authService;
        private readonly INavigationService _navigationService;

        public MainViewModel(IAuthService authService, INavigationService navigationService)
        {
            _authService = authService;
            _navigationService = navigationService;
            Title = "PetSitter Connect";
        }

        public override async Task InitializeAsync()
        {
            await base.InitializeAsync();
            // Initialize main view data
        }
    }
}
