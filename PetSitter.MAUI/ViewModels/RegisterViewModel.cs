using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitter.MAUI.Models;
using PetSitter.MAUI.Services;
using System.ComponentModel.DataAnnotations;

namespace PetSitter.MAUI.ViewModels
{
    public partial class RegisterViewModel : BaseViewModel
    {
        private readonly IAuthService _authService;
        private readonly INavigationService _navigationService;

        [ObservableProperty]
        [Required(ErrorMessage = "First name is required")]
        private string firstName = string.Empty;

        [ObservableProperty]
        [Required(ErrorMessage = "Last name is required")]
        private string lastName = string.Empty;

        [ObservableProperty]
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        private string email = string.Empty;

        [ObservableProperty]
        [Required(ErrorMessage = "Password is required")]
        [MinLength(6, ErrorMessage = "Password must be at least 6 characters")]
        private string password = string.Empty;

        [ObservableProperty]
        [Required(ErrorMessage = "Please confirm your password")]
        private string confirmPassword = string.Empty;

        [ObservableProperty]
        [Required(ErrorMessage = "Please select a role")]
        private string selectedRole = string.Empty;

        [ObservableProperty]
        private bool acceptTerms;

        public List<string> UserRoles { get; } = new() { "PetOwner", "PetSitter" };

        public RegisterViewModel(IAuthService authService, INavigationService navigationService)
        {
            _authService = authService;
            _navigationService = navigationService;
            Title = "Register";
        }

        [RelayCommand]
        private async Task RegisterAsync()
        {
            await ExecuteAsync(async () =>
            {
                if (!ValidateInput())
                    return;

                var request = new AuthRequest
                {
                    FirstName = FirstName,
                    LastName = LastName,
                    Email = Email,
                    Password = Password,
                    Role = SelectedRole
                };

                var result = await _authService.RegisterAsync(request);

                if (result.Success)
                {
                    await _navigationService.DisplayAlertAsync("Success", 
                        "Registration successful! Please login with your credentials.", "OK");
                    await _navigationService.NavigateBackAsync();
                }
                else
                {
                    SetError(result.Error ?? "Registration failed");
                }
            }, "Creating account...");
        }

        [RelayCommand]
        private async Task NavigateToLoginAsync()
        {
            await _navigationService.NavigateBackAsync();
        }

        [RelayCommand]
        private async Task ShowTermsAsync()
        {
            await _navigationService.DisplayAlertAsync("Terms & Conditions", 
                "Terms and conditions content will be displayed here.", "OK");
        }

        private bool ValidateInput()
        {
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(this);

            if (!Validator.TryValidateObject(this, validationContext, validationResults, true))
            {
                var errors = string.Join("\n", validationResults.Select(r => r.ErrorMessage));
                SetError(errors);
                return false;
            }

            // Additional validation
            if (Password != ConfirmPassword)
            {
                SetError("Passwords do not match");
                return false;
            }

            if (string.IsNullOrEmpty(SelectedRole))
            {
                SetError("Please select whether you are a Pet Owner or Pet Sitter");
                return false;
            }

            if (!AcceptTerms)
            {
                SetError("Please accept the terms and conditions");
                return false;
            }

            ClearError();
            return true;
        }
    }
}
