using PetSitter.MAUI.Services;

namespace PetSitter.MAUI.ViewModels
{
    public partial class ProfileViewModel : BaseViewModel
    {
        private readonly IAuthService _authService;
        private readonly IApiService _apiService;

        public ProfileViewModel(IAuthService authService, IApiService apiService)
        {
            _authService = authService;
            _apiService = apiService;
            Title = "Profile";
        }
    }
}
