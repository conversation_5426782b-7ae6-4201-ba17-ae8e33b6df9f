using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.ComponentModel;

namespace PetSitter.MAUI.ViewModels
{
    public partial class BaseViewModel : ObservableObject
    {
        [ObservableProperty]
        private bool isBusy;

        [ObservableProperty]
        private string title = string.Empty;

        [ObservableProperty]
        private bool isRefreshing;

        [ObservableProperty]
        private string errorMessage = string.Empty;

        [ObservableProperty]
        private bool hasError;

        public virtual async Task InitializeAsync()
        {
            await Task.CompletedTask;
        }

        public virtual async Task OnAppearingAsync()
        {
            await Task.CompletedTask;
        }

        public virtual async Task OnDisappearingAsync()
        {
            await Task.CompletedTask;
        }

        [RelayCommand]
        public virtual async Task RefreshAsync()
        {
            IsRefreshing = true;
            try
            {
                await LoadDataAsync();
            }
            finally
            {
                IsRefreshing = false;
            }
        }

        protected virtual async Task LoadDataAsync()
        {
            await Task.CompletedTask;
        }

        protected void SetError(string message)
        {
            ErrorMessage = message;
            HasError = !string.IsNullOrEmpty(message);
        }

        protected void ClearError()
        {
            ErrorMessage = string.Empty;
            HasError = false;
        }

        protected async Task ExecuteAsync(Func<Task> operation, string? loadingMessage = null)
        {
            if (IsBusy)
                return;

            try
            {
                IsBusy = true;
                ClearError();
                
                if (!string.IsNullOrEmpty(loadingMessage))
                {
                    // Could show loading message in UI
                }

                await operation();
            }
            catch (Exception ex)
            {
                SetError(ex.Message);
                System.Diagnostics.Debug.WriteLine($"Error in {GetType().Name}: {ex}");
            }
            finally
            {
                IsBusy = false;
            }
        }

        protected async Task<T?> ExecuteAsync<T>(Func<Task<T>> operation, string? loadingMessage = null)
        {
            if (IsBusy)
                return default;

            try
            {
                IsBusy = true;
                ClearError();
                
                if (!string.IsNullOrEmpty(loadingMessage))
                {
                    // Could show loading message in UI
                }

                return await operation();
            }
            catch (Exception ex)
            {
                SetError(ex.Message);
                System.Diagnostics.Debug.WriteLine($"Error in {GetType().Name}: {ex}");
                return default;
            }
            finally
            {
                IsBusy = false;
            }
        }
    }
}
