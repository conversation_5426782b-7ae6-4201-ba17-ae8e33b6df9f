using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using PetSitter.MAUI.Models;
using PetSitter.MAUI.Services;
using System.ComponentModel.DataAnnotations;

namespace PetSitter.MAUI.ViewModels
{
    public partial class LoginViewModel : BaseViewModel
    {
        private readonly IAuthService _authService;
        private readonly INavigationService _navigationService;

        [ObservableProperty]
        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        private string email = string.Empty;

        [ObservableProperty]
        [Required(ErrorMessage = "Password is required")]
        [MinLength(6, ErrorMessage = "Password must be at least 6 characters")]
        private string password = string.Empty;

        [ObservableProperty]
        private bool rememberMe;

        public LoginViewModel(IAuthService authService, INavigationService navigationService)
        {
            _authService = authService;
            _navigationService = navigationService;
            Title = "Login";
        }

        [RelayCommand]
        private async Task LoginAsync()
        {
            await ExecuteAsync(async () =>
            {
                if (!ValidateInput())
                    return;

                var request = new LoginRequest
                {
                    Email = Email,
                    Password = Password
                };

                var result = await _authService.LoginAsync(request);

                if (result.Success)
                {
                    // Navigate to main app
                    await _navigationService.NavigateToAsync("//main");
                }
                else
                {
                    SetError(result.Error ?? "Login failed");
                }
            }, "Logging in...");
        }

        [RelayCommand]
        private async Task NavigateToRegisterAsync()
        {
            await _navigationService.NavigateToAsync("register");
        }

        [RelayCommand]
        private async Task ForgotPasswordAsync()
        {
            await _navigationService.DisplayAlertAsync("Forgot Password", 
                "Password reset functionality will be implemented soon.", "OK");
        }

        private bool ValidateInput()
        {
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(this);

            if (!Validator.TryValidateObject(this, validationContext, validationResults, true))
            {
                var errors = string.Join("\n", validationResults.Select(r => r.ErrorMessage));
                SetError(errors);
                return false;
            }

            ClearError();
            return true;
        }

        public override async Task OnAppearingAsync()
        {
            await base.OnAppearingAsync();
            
            // Check if user is already authenticated
            if (await _authService.IsAuthenticatedAsync())
            {
                await _navigationService.NavigateToAsync("//main");
            }
        }
    }
}
