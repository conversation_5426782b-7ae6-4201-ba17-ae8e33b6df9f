<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitter.MAUI.Views.LoginPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitter.MAUI.ViewModels"
             Title="{Binding Title}"
             Shell.NavBarIsVisible="False">

    <ContentPage.BindingContext>
        <vm:LoginViewModel />
    </ContentPage.BindingContext>

    <ScrollView>
        <Grid RowDefinitions="Auto,*,Auto" Padding="20">
            
            <!-- Header -->
            <StackLayout Grid.Row="0" Spacing="10" Margin="0,40,0,40">
                <Image Source="dotnet_bot.png" 
                       HeightRequest="100" 
                       WidthRequest="100" 
                       HorizontalOptions="Center" />
                <Label Text="PetSitter Connect" 
                       FontSize="28" 
                       FontAttributes="Bold" 
                       HorizontalOptions="Center" 
                       TextColor="{StaticResource Primary}" />
                <Label Text="Welcome back! Please sign in to your account." 
                       FontSize="16" 
                       HorizontalOptions="Center" 
                       TextColor="{StaticResource Gray600}" />
            </StackLayout>

            <!-- Login Form -->
            <StackLayout Grid.Row="1" Spacing="20">
                
                <!-- Email Entry -->
                <Frame BackgroundColor="White" HasShadow="False" BorderColor="{StaticResource Gray200}">
                    <Entry Placeholder="Email" 
                           Text="{Binding Email}" 
                           Keyboard="Email" 
                           ReturnType="Next" />
                </Frame>

                <!-- Password Entry -->
                <Frame BackgroundColor="White" HasShadow="False" BorderColor="{StaticResource Gray200}">
                    <Entry Placeholder="Password" 
                           Text="{Binding Password}" 
                           IsPassword="True" 
                           ReturnType="Done" />
                </Frame>

                <!-- Remember Me -->
                <StackLayout Orientation="Horizontal">
                    <CheckBox IsChecked="{Binding RememberMe}" />
                    <Label Text="Remember me" VerticalOptions="Center" />
                </StackLayout>

                <!-- Error Message -->
                <Label Text="{Binding ErrorMessage}" 
                       TextColor="Red" 
                       IsVisible="{Binding HasError}" 
                       FontSize="14" />

                <!-- Login Button -->
                <Button Text="Sign In" 
                        Command="{Binding LoginCommand}" 
                        BackgroundColor="{StaticResource Primary}" 
                        TextColor="White" 
                        HeightRequest="50" 
                        FontSize="16" 
                        FontAttributes="Bold" 
                        IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" />

                <!-- Loading Indicator -->
                <ActivityIndicator IsVisible="{Binding IsBusy}" 
                                   IsRunning="{Binding IsBusy}" 
                                   Color="{StaticResource Primary}" />

                <!-- Forgot Password -->
                <Button Text="Forgot Password?" 
                        Command="{Binding ForgotPasswordCommand}" 
                        BackgroundColor="Transparent" 
                        TextColor="{StaticResource Primary}" 
                        FontSize="14" />

            </StackLayout>

            <!-- Footer -->
            <StackLayout Grid.Row="2" Orientation="Horizontal" HorizontalOptions="Center" Spacing="5">
                <Label Text="Don't have an account?" TextColor="{StaticResource Gray600}" />
                <Button Text="Sign Up" 
                        Command="{Binding NavigateToRegisterCommand}" 
                        BackgroundColor="Transparent" 
                        TextColor="{StaticResource Primary}" 
                        FontSize="14" 
                        Padding="0" />
            </StackLayout>

        </Grid>
    </ScrollView>

</ContentPage>
