<?xml version="1.0" encoding="utf-8" ?>
<ContentPage x:Class="PetSitter.MAUI.Views.RegisterPage"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:vm="clr-namespace:PetSitter.MAUI.ViewModels"
             Title="{Binding Title}">

    <ContentPage.BindingContext>
        <vm:RegisterViewModel />
    </ContentPage.BindingContext>

    <ScrollView>
        <Grid RowDefinitions="Auto,*,Auto" Padding="20">
            
            <!-- Header -->
            <StackLayout Grid.Row="0" Spacing="10" Margin="0,20,0,30">
                <Label Text="Create Account" 
                       FontSize="28" 
                       FontAttributes="Bold" 
                       HorizontalOptions="Center" 
                       TextColor="{StaticResource Primary}" />
                <Label Text="Join <PERSON><PERSON><PERSON> Connect and start your journey!" 
                       FontSize="16" 
                       HorizontalOptions="Center" 
                       TextColor="{StaticResource Gray600}" />
            </StackLayout>

            <!-- Registration Form -->
            <StackLayout Grid.Row="1" Spacing="15">
                
                <!-- First Name -->
                <Frame BackgroundColor="White" HasShadow="False" BorderColor="{StaticResource Gray200}">
                    <Entry Placeholder="First Name" 
                           Text="{Binding FirstName}" 
                           ReturnType="Next" />
                </Frame>

                <!-- Last Name -->
                <Frame BackgroundColor="White" HasShadow="False" BorderColor="{StaticResource Gray200}">
                    <Entry Placeholder="Last Name" 
                           Text="{Binding LastName}" 
                           ReturnType="Next" />
                </Frame>

                <!-- Email -->
                <Frame BackgroundColor="White" HasShadow="False" BorderColor="{StaticResource Gray200}">
                    <Entry Placeholder="Email" 
                           Text="{Binding Email}" 
                           Keyboard="Email" 
                           ReturnType="Next" />
                </Frame>

                <!-- Password -->
                <Frame BackgroundColor="White" HasShadow="False" BorderColor="{StaticResource Gray200}">
                    <Entry Placeholder="Password" 
                           Text="{Binding Password}" 
                           IsPassword="True" 
                           ReturnType="Next" />
                </Frame>

                <!-- Confirm Password -->
                <Frame BackgroundColor="White" HasShadow="False" BorderColor="{StaticResource Gray200}">
                    <Entry Placeholder="Confirm Password" 
                           Text="{Binding ConfirmPassword}" 
                           IsPassword="True" 
                           ReturnType="Done" />
                </Frame>

                <!-- Role Selection -->
                <Label Text="I am a:" FontSize="16" FontAttributes="Bold" />
                <Frame BackgroundColor="White" HasShadow="False" BorderColor="{StaticResource Gray200}">
                    <Picker Title="Select your role" 
                            ItemsSource="{Binding UserRoles}" 
                            SelectedItem="{Binding SelectedRole}" />
                </Frame>

                <!-- Terms and Conditions -->
                <StackLayout Orientation="Horizontal">
                    <CheckBox IsChecked="{Binding AcceptTerms}" />
                    <StackLayout Orientation="Horizontal" VerticalOptions="Center">
                        <Label Text="I accept the " VerticalOptions="Center" />
                        <Button Text="Terms & Conditions" 
                                Command="{Binding ShowTermsCommand}" 
                                BackgroundColor="Transparent" 
                                TextColor="{StaticResource Primary}" 
                                FontSize="14" 
                                Padding="0" 
                                VerticalOptions="Center" />
                    </StackLayout>
                </StackLayout>

                <!-- Error Message -->
                <Label Text="{Binding ErrorMessage}" 
                       TextColor="Red" 
                       IsVisible="{Binding HasError}" 
                       FontSize="14" />

                <!-- Register Button -->
                <Button Text="Create Account" 
                        Command="{Binding RegisterCommand}" 
                        BackgroundColor="{StaticResource Primary}" 
                        TextColor="White" 
                        HeightRequest="50" 
                        FontSize="16" 
                        FontAttributes="Bold" 
                        IsEnabled="{Binding IsBusy, Converter={StaticResource InvertedBoolConverter}}" />

                <!-- Loading Indicator -->
                <ActivityIndicator IsVisible="{Binding IsBusy}" 
                                   IsRunning="{Binding IsBusy}" 
                                   Color="{StaticResource Primary}" />

            </StackLayout>

            <!-- Footer -->
            <StackLayout Grid.Row="2" Orientation="Horizontal" HorizontalOptions="Center" Spacing="5" Margin="0,20,0,0">
                <Label Text="Already have an account?" TextColor="{StaticResource Gray600}" />
                <Button Text="Sign In" 
                        Command="{Binding NavigateToLoginCommand}" 
                        BackgroundColor="Transparent" 
                        TextColor="{StaticResource Primary}" 
                        FontSize="14" 
                        Padding="0" />
            </StackLayout>

        </Grid>
    </ScrollView>

</ContentPage>
