namespace PetSitter.MAUI.Models
{
    public class ChatMessage
    {
        public int Id { get; set; }
        public string SenderId { get; set; } = string.Empty;
        public string SenderName { get; set; } = string.Empty;
        public string ReceiverId { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public int? BookingId { get; set; }
        public bool IsRead { get; set; }
        
        // UI Properties
        public bool IsFromCurrentUser { get; set; }
        public string TimeDisplay => Timestamp.ToString("HH:mm");
        public string DateDisplay => Timestamp.ToString("MMM dd, yyyy");
    }

    public class ChatRoom
    {
        public string Id { get; set; } = string.Empty;
        public int BookingId { get; set; }
        public string OwnerId { get; set; } = string.Empty;
        public string OwnerName { get; set; } = string.Empty;
        public string SitterId { get; set; } = string.Empty;
        public string SitterName { get; set; } = string.Empty;
        public List<ChatMessage> Messages { get; set; } = new();
        public DateTime LastActivity { get; set; } = DateTime.UtcNow;
        
        // UI Properties
        public string DisplayName { get; set; } = string.Empty;
        public string LastMessage => Messages.LastOrDefault()?.Message ?? "No messages";
        public string LastMessageTime => Messages.LastOrDefault()?.TimeDisplay ?? "";
        public bool HasUnreadMessages { get; set; }
        public int UnreadCount { get; set; }
    }
}
