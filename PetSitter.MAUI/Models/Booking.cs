using System.ComponentModel.DataAnnotations;

namespace PetSitter.MAUI.Models
{
    public class Booking
    {
        public int Id { get; set; }

        [Required]
        public int PetCareRequestId { get; set; }

        public virtual PetCareRequest? Request { get; set; }

        [Required]
        public string OwnerId { get; set; } = string.Empty;

        public virtual UserProfile? Owner { get; set; }

        [Required]
        public string SitterId { get; set; } = string.Empty;

        public virtual UserProfile? Sitter { get; set; }

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        public decimal AgreedPrice { get; set; }

        public BookingStatus Status { get; set; } = BookingStatus.Confirmed;
        public PaymentStatus PaymentStatus { get; set; } = PaymentStatus.Pending;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        // Additional properties for UI
        public string StatusDisplay => Status.ToString();
        public string PaymentStatusDisplay => PaymentStatus.ToString();
        public string DateRange => $"{StartDate:MMM dd} - {EndDate:MMM dd, yyyy}";
        public int DurationDays => (EndDate - StartDate).Days + 1;
        public string PriceDisplay => $"${AgreedPrice:F2}";
        public string OwnerName => Owner?.FullName ?? "Unknown";
        public string SitterName => Sitter?.FullName ?? "Unknown";
        public bool IsActive => Status == BookingStatus.Confirmed;
        public bool IsCompleted => Status == BookingStatus.Completed;
        public bool CanComplete => Status == BookingStatus.Confirmed && DateTime.Now >= EndDate;
        public string PetTypeDisplay => Request?.PetTypeDisplay ?? "Unknown";
    }
}
