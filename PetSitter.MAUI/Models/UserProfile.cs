using System.ComponentModel.DataAnnotations;

namespace PetSitter.MAUI.Models
{
    public class UserProfile
    {
        public string Id { get; set; } = string.Empty;
        
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;
        
        [Required]
        public string UserName { get; set; } = string.Empty;
        
        [Required]
        [StringLength(50)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        public string Role { get; set; } = string.Empty; // PetOwner/PetSitter

        public string? ProfileBio { get; set; }
        public string? ProfileImageUrl { get; set; }
        public DateTime RegistrationDate { get; set; } = DateTime.UtcNow;
        
        // Additional properties for UI
        public string FullName => $"{FirstName} {LastName}";
        public bool IsPetOwner => Role == "PetOwner";
        public bool IsPetSitter => Role == "PetSitter";
    }
}
