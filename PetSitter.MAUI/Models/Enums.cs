namespace PetSitter.MAUI.Models
{
    public enum PetType
    {
        <PERSON>,
        <PERSON>,
        <PERSON>,
        <PERSON>
    }

    public enum RequestStatus
    {
        Pending,
        Accepted,
        Completed,
        Canceled
    }

    public enum BookingStatus
    {
        Confirmed,
        Completed,
        Canceled
    }

    public enum PaymentStatus
    {
        Pending,
        HeldInEscrow,
        Released,
        Refunded
    }

    public enum UserRole
    {
        PetOwner,
        PetSitter
    }
}
