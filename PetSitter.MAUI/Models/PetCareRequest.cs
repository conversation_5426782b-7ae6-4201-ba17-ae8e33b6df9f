using System.ComponentModel.DataAnnotations;

namespace PetSitter.MAUI.Models
{
    public class PetCareRequest
    {
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        public virtual UserProfile? Owner { get; set; }

        [Required]
        public PetType PetType { get; set; }

        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Required]
        public DateTime StartDate { get; set; }

        [Required]
        public DateTime EndDate { get; set; }

        [Required]
        [StringLength(200)]
        public string Location { get; set; } = string.Empty;

        public decimal Budget { get; set; }

        public RequestStatus Status { get; set; } = RequestStatus.Pending;
        public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
        
        // Additional properties for UI
        public string PetTypeDisplay => PetType.ToString();
        public string StatusDisplay => Status.ToString();
        public string DateRange => $"{StartDate:MMM dd} - {EndDate:MMM dd, yyyy}";
        public int DurationDays => (EndDate - StartDate).Days + 1;
        public string BudgetDisplay => $"${Budget:F2}";
        public bool IsActive => Status == RequestStatus.Pending || Status == RequestStatus.Accepted;
        public string OwnerName => Owner?.FullName ?? "Unknown";
    }
}
